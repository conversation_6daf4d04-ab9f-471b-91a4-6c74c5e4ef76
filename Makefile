.PHONY: help install install-dev dev build up down logs clean test lint format security deploy-staging deploy-prod backup health

help:
	@echo "Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  install       - Install production dependencies"
	@echo "  install-dev   - Install development dependencies"
	@echo "  dev           - Run development server"
	@echo "  format        - Format code with black and isort"
	@echo "  lint          - Run linting (flake8, mypy)"
	@echo "  security      - Run security checks (bandit, safety)"
	@echo "  test          - Run tests with coverage"
	@echo "  test-watch    - Run tests in watch mode"
	@echo ""
	@echo "Docker:"
	@echo "  build         - Build Docker image"
	@echo "  up            - Start all services with Docker Compose"
	@echo "  down          - Stop all services"
	@echo "  logs          - View logs"
	@echo "  clean         - Clean up Docker resources"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-staging - Deploy to staging environment"
	@echo "  deploy-prod   - Deploy to production environment"
	@echo "  backup        - Create database backup"
	@echo "  health        - Check application health"
	@echo ""
	@echo "CI/CD:"
	@echo "  ci-setup      - Setup CI/CD tools"
	@echo "  pre-commit    - Setup pre-commit hooks"

# Development
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install -r requirements-dev.txt

dev:
	python run_dev.py

format:
	black app/ tests/
	isort app/ tests/

lint:
	flake8 app/ tests/ --max-line-length=88 --extend-ignore=E203,W503
	mypy app/ --ignore-missing-imports

security:
	bandit -r app/ -f json -o bandit-report.json
	safety check --json --output safety-report.json
	pip-audit --requirement requirements.txt --format=json --output=audit-report.json

test:
	pytest tests/ -v --cov=app --cov-report=html --cov-report=term-missing

test-watch:
	pytest-watch tests/ -- -v --cov=app

# Docker
build:
	docker build -t banachef-server .

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f

clean:
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

# Production deployment
deploy-staging:
	./scripts/deploy.sh deploy staging

deploy-prod:
	./scripts/deploy.sh deploy production

backup:
	./scripts/deploy.sh backup

health:
	./scripts/deploy.sh health

# CI/CD setup
ci-setup: install-dev pre-commit
	@echo "CI/CD tools setup complete"

pre-commit:
	pre-commit install
	pre-commit install --hook-type commit-msg

# Database
migrate:
	python create_tables.py

# Monitoring
monitoring-up:
	docker-compose -f docker-compose.prod.yml --profile monitoring up -d

monitoring-down:
	docker-compose -f docker-compose.prod.yml --profile monitoring down
