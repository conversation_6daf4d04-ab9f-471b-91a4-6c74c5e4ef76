services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: banachef_db
    environment:
      POSTGRES_DB: banachef_db
      POSTGRES_USER: ${DB_USER:-banachef_admin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-123453Ago@}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-banachef_admin} -d banachef_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Application
  api:
    build: .
    container_name: banachef_api
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-banachef_admin}:${DB_PASSWORD:-123453Ago@}@db:5432/banachef_db
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-secret-key-change-in-production}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - DEBUG=${DEBUG:-True}
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./app:/app/app
      - ./.env:/app/.env
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Redis (for caching - optional)
  redis:
    image: redis:7-alpine
    container_name: banachef_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
