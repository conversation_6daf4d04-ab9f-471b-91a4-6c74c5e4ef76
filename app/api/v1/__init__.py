from fastapi import APIRouter

# Import routers
from app.api.v1 import auth, referrals, subscriptions
# from app.api.v1 import users, recipes, image_scan

api_router = APIRouter()

# Include routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(referrals.router, prefix="/referrals", tags=["referrals"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
# api_router.include_router(users.router, prefix="/users", tags=["users"])
# api_router.include_router(recipes.router, prefix="/recipes", tags=["recipes"])
# api_router.include_router(image_scan.router, prefix="/scans", tags=["image-scan"])