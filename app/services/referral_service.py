import re
import random
import string
from typing import Optional
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.user import User
from app.models.referral_history import ReferralHistory
from app.models.referral_voucher import ReferralVoucher
from app.crud.crud_referral import referral_voucher, referral_history, referral_user
from app.schemas.referral import (
    ReferralHistoryCreate,
    ReferralVoucherCreate,
    ReferralStatsResponse
)


class ReferralService:
    
    @staticmethod
    def normalize_vietnamese_name(name: str) -> str:
        """Normalize Vietnamese name for referral code generation"""
        if not name:
            return "USER"
            
        # Remove Vietnamese accents and convert to uppercase
        vietnamese_chars = {
            'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a', 'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
            'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
            'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
            'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o', 'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
            'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u', 'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
            'đ': 'd'
        }
        
        # Convert to lowercase first
        normalized = name.lower()
        
        # Replace Vietnamese characters
        for viet_char, latin_char in vietnamese_chars.items():
            normalized = normalized.replace(viet_char, latin_char)
        
        # Keep only alphanumeric characters
        normalized = re.sub(r'[^a-z0-9]', '', normalized)
        
        # Take first 6 characters and convert to uppercase
        return normalized[:6].upper() if normalized else "USER"

    @staticmethod
    def generate_referral_code(db: Session, user_name: str, max_attempts: int = 100) -> str:
        """Generate unique referral code for user"""
        normalized_name = ReferralService.normalize_vietnamese_name(user_name)
        
        for attempt in range(max_attempts):
            # Generate random number (3 digits)
            random_number = random.randint(100, 999)
            
            # Create referral code
            referral_code = f"BANACHEF-{normalized_name}{random_number}"
            
            # Check if code already exists
            existing_user = db.query(User).filter(User.referral_code == referral_code).first()
            if not existing_user:
                return referral_code
        
        # If all attempts failed, use UUID-based approach
        import uuid
        unique_suffix = str(uuid.uuid4())[:8].upper()
        return f"BANACHEF-{normalized_name}{unique_suffix}"

    @staticmethod
    def apply_referral_code(db: Session, user_id: UUID, referral_code: str) -> dict:
        """Apply referral code for a new user"""
        try:
            # Get the user applying the code
            user = db.query(User).filter(User.user_id == user_id).first()
            if not user:
                return {"success": False, "message": "User not found"}
            
            # Check if user already has a referrer
            if user.referred_by_user_id:
                return {"success": False, "message": "You have already applied a referral code"}
            
            # Check if user has already made a purchase
            if user.has_made_first_purchase:
                return {"success": False, "message": "Referral codes can only be applied before making your first purchase"}
            
            # Find the referrer by referral code
            referrer = referral_user.get_by_referral_code(db, referral_code)
            if not referrer:
                return {"success": False, "message": "Invalid referral code"}
            
            # Check if user is trying to refer themselves
            if referrer.user_id == user_id:
                return {"success": False, "message": "You cannot refer yourself"}
            
            # Update user's referral information
            referral_user.update_referral_info(db, user_id, referrer.user_id)
            
            # Create referral history record
            referral_history.create(db, obj_in=ReferralHistoryCreate(
                referrer_user_id=referrer.user_id,
                referred_user_id=user_id,
                status="pending"
            ))
            
            return {
                "success": True,
                "message": f"Successfully applied referral code from {referrer.display_name or referrer.email}",
                "referrer_display_name": referrer.display_name
            }
            
        except Exception as e:
            db.rollback()
            return {"success": False, "message": f"An error occurred: {str(e)}"}

    @staticmethod
    def process_first_purchase(db: Session, user_id: UUID, applied_vouchers: list = None) -> dict:
        """Process user's first purchase and handle referral rewards"""
        try:
            # Get user
            user = db.query(User).filter(User.user_id == user_id).first()
            if not user:
                return {"success": False, "message": "User not found"}
            
            # Mark user as having made first purchase
            referral_user.mark_first_purchase(db, user_id)
            
            # Mark applied vouchers as used
            if applied_vouchers:
                for voucher_id in applied_vouchers:
                    referral_voucher.mark_as_used(db, voucher_id)
            
            # If user was referred, reward the referrer
            if user.referred_by_user_id:
                # Find the referral history record
                history = referral_history.get_by_referred_user(db, user_id)
                if history and history.status == "pending":
                    # Mark referral as completed
                    referral_history.mark_as_completed(db, history.id)
                    
                    # Create voucher for referrer
                    referral_voucher.create(db, obj_in=ReferralVoucherCreate(
                        owner_user_id=user.referred_by_user_id,
                        source_referral_id=history.id,
                        amount_usd=5.00,
                        status="available"
                    ))
                    
                    return {
                        "success": True,
                        "message": "Purchase processed and referrer rewarded",
                        "referrer_rewarded": True
                    }
            
            return {"success": True, "message": "Purchase processed successfully"}
            
        except Exception as e:
            db.rollback()
            return {"success": False, "message": f"An error occurred: {str(e)}"}

    @staticmethod
    def get_user_referral_stats(db: Session, user_id: UUID) -> Optional[ReferralStatsResponse]:
        """Get referral statistics for a user"""
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user or not user.referral_code:
            return None
        
        # Get referral history
        referrals = referral_history.get_by_referrer(db, user_id)
        total_referrals = len(referrals)
        completed_referrals = len([r for r in referrals if r.status == "completed"])
        pending_referrals = len([r for r in referrals if r.status == "pending"])
        
        # Get available vouchers
        available_vouchers_list = referral_voucher.get_available_vouchers(db, user_id)
        available_vouchers = len(available_vouchers_list)
        total_voucher_value = sum(float(v.amount_usd) for v in available_vouchers_list)
        
        return ReferralStatsResponse(
            referral_code=user.referral_code,
            total_referrals=total_referrals,
            completed_referrals=completed_referrals,
            pending_referrals=pending_referrals,
            available_vouchers=available_vouchers,
            total_voucher_value_usd=total_voucher_value
        )


# Create service instance
referral_service = ReferralService()
