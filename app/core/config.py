from typing import List, Optional, Union
from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application Settings
    APP_NAME: str = "Bana Chef Server"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = False
    SECRET_KEY: str = "dev-secret-key-change-in-production"

    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # Database Configuration
    DATABASE_URL: str = "postgresql://banachef_admin:123453Ago@localhost:5432/banachef_db"
    DATABASE_URL_DEV: Optional[str] = None
    DB_USER: Optional[str] = None
    DB_PASSWORD: Optional[str] = None

    # JWT Configuration
    JWT_SECRET_KEY: str = "dev-jwt-secret-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 3600

    # Google AI Configuration
    GOOGLE_API_KEY: str = ""

    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: str = "************-8ulqb0lq7fgvofmpgrdaia3c6ho7u1mc.apps.googleusercontent.com"
    GOOGLE_CLIENT_SECRET: str = "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"

    # Google Cloud Storage Configuration
    GOOGLE_CLOUD_PROJECT_ID: str = "banachef"
    GOOGLE_CLOUD_STORAGE_BUCKET: str = ""
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = "credentials/banachef-service-account.json"

    # CORS Configuration
    ALLOWED_ORIGINS: str = "*"

    # File Upload Configuration
    MAX_FILE_SIZE: int = ********  # 10MB
    ALLOWED_FILE_TYPES: str = "image/jpeg,image/png,image/webp"

    # Logging Configuration
    LOG_LEVEL: str = "INFO"

    # Firebase Configuration
    FIREBASE_PROJECT_ID: str = "banachef"
    FIREBASE_CREDENTIALS_PATH: Optional[str] = "credentials/firebase-service-account.json"

    # Redis Configuration
    REDIS_URL: str = "redis://redis:6379/0"

    # Celery Configuration
    CELERY_BROKER_URL: str = "redis://redis:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://redis:6379/0"

    # Push Notification Configuration
    NOTIFICATION_BATCH_SIZE: int = 100
    NOTIFICATION_RETRY_ATTEMPTS: int = 3
    NOTIFICATION_DEFAULT_LANGUAGE: str = "vi"
    NOTIFICATION_RATE_LIMIT_PER_USER: int = 50  # per hour

    # Scheduled Notification Configuration
    ENABLE_SCHEDULED_NOTIFICATIONS: bool = True
    DAILY_RECIPE_SUGGESTION_TIME: str = "09:00"  # UTC time
    WEEKLY_SUMMARY_DAY: int = 0  # Monday = 0

    @property
    def cors_origins(self) -> List[str]:
        """Parse CORS origins from string to list"""
        if self.ALLOWED_ORIGINS == "*":
            return ["*"]
        return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(",") if origin.strip()]

    @property
    def file_types_list(self) -> List[str]:
        """Parse file types from string to list"""
        return [file_type.strip() for file_type in self.ALLOWED_FILE_TYPES.split(",") if file_type.strip()]

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
